import React, { useState } from 'react';
import { Dropdown, Checkbox, Space, Input, Button, Menu, Divider } from 'antd';
import { SearchOutlined } from '@ant-design/icons';
import IconSet from '@/modules-smb/Wireless/assets/Monitor/Logo_Set.png';
import { DragDropContext, Droppable, Draggable } from '@hello-pangea/dnd';

interface ColumnSelectorProps {
  columns: any[];
  visibleColumns: string[];
  onChange: (visibleColumns: string[]) => void;
  draggable?: boolean; // 添加 draggable 属性
  onDragEnd?: (newOrder: string[]) => void; // 添加拖拽结束回调
}

const ColumnSelector: React.FC<ColumnSelectorProps> = ({ columns, visibleColumns, onChange, draggable = true, onDragEnd }) => {
  const shouldShowSearch = columns.length > 1;
  const [dropdownVisible, setDropdownVisible] = useState(false);
  const [searchText, setSearchText] = useState('');

  // 搜索过滤列
  const filteredColumns = columns.filter(column =>
    String(column.title).toLowerCase().includes(searchText.toLowerCase())
  );

  // 根据visibleColumns的顺序对filteredColumns进行排序
  const orderedFilteredColumns = [...filteredColumns].sort((a, b) => {
    const indexA = visibleColumns.indexOf(a.key);
    const indexB = visibleColumns.indexOf(b.key);
    // 如果列不在visibleColumns中，将其放在最后
    if (indexA === -1) return 1;
    if (indexB === -1) return -1;
    return indexA - indexB;
  });

  // 1. 实时切换单个列（禁止取消固定列）
  const toggleColumn = (columnKey: string) => {
    const column = columns.find(col => col.key === columnKey);
    if (column?.columnsFix) return; // 固定列不可取消

    // 计算新的可见列
    const newVisibleColumns = visibleColumns.includes(columnKey)
      ? visibleColumns.filter(key => key !== columnKey)
      : [...visibleColumns, columnKey];
    const fixedKeys = columns.filter(col => col.columnsFix).map(col => col.key);
    const merged = Array.from(new Set([...newVisibleColumns, ...fixedKeys]));

    onChange(merged);
  };

  const toggleAllColumns = () => {
    const fixedKeys = columns.filter(col => col.columnsFix).map(col => col.key);
    const nonFixedKeys = columns.filter(col => !col.columnsFix).map(col => col.key);
    const allNonFixedSelected = nonFixedKeys.every(key => visibleColumns.includes(key));

    const newVisibleColumns = allNonFixedSelected
      ? fixedKeys
      : [...fixedKeys, ...nonFixedKeys];

    onChange(newVisibleColumns);
  };

  // 处理拖拽结束事件
  const handleDragEnd = (result: any) => {
    // 如果 draggable 为 false，则不处理拖拽
    if (!draggable) return;

    console.log('ColumnSelector handleDragEnd triggered:', result);
    const { source, destination } = result;

    // 如果没有目标位置或在原位置则不处理
    if (!destination || source.index === destination.index) {
      return;
    }

    // 重新排序列 - 仅对当前显示的列进行排序
    const reorderedColumns = Array.from(orderedFilteredColumns);
    const [movedColumn] = reorderedColumns.splice(source.index, 1);
    reorderedColumns.splice(destination.index, 0, movedColumn);

    // 获取新的列顺序
    const newOrder = reorderedColumns.map(col => col.key);

    // 保持原有的可见列，只改变它们的顺序
    const updatedVisibleColumns = [...visibleColumns];

    // 根据新顺序重新排列 visibleColumns
    updatedVisibleColumns.sort((a, b) => {
      const indexA = newOrder.indexOf(a);
      const indexB = newOrder.indexOf(b);
      // 如果列不在 newOrder 中，将其放在最后
      if (indexA === -1) return 1;
      if (indexB === -1) return -1;
      return indexA - indexB;
    });

    // 调用可见列变化回调
    onChange(updatedVisibleColumns);

    // 如果提供了拖拽结束回调，也调用它来保存列顺序
    if (onDragEnd) {
      onDragEnd(newOrder);
    }
  };

  const menu = (
    <Menu style={{
      padding: '16px',
      width: '250px',
      maxHeight: '434px',
      overflowY: 'auto',
      border: 'none', // 边框
      boxShadow: '0 4px 16px rgba(0, 0, 0, 0.1)', // 投影效果
    }}>
      {shouldShowSearch && (
        <Input
          prefix={<SearchOutlined />}
          value={searchText}
          onChange={(e) => setSearchText(e.target.value)}
          style={{ marginBottom: '12px' }}
        />
      )}

      {/* 全选选项 */}
      <div style={{ marginBottom: '8px' }}>
        <Checkbox
          checked={columns.filter(col => !col.columnsFix).every(col => visibleColumns.includes(col.key))}
          indeterminate={
            columns.filter(col => !col.columnsFix).some(col => visibleColumns.includes(col.key)) &&
            columns.filter(col => !col.columnsFix).some(col => !visibleColumns.includes(col.key))
          }
          onChange={toggleAllColumns}
        >
          ALL
        </Checkbox>
      </div>

      {/* 列选项列表 - 添加拖拽功能 */}
      <DragDropContext onDragEnd={handleDragEnd}>
        <Droppable droppableId="columnSelector">
          {(provided) => (
            <div {...provided.droppableProps} ref={provided.innerRef}>
              {orderedFilteredColumns.map((column, index) => (
                <Draggable
                  key={column.key}
                  draggableId={column.key}
                  index={index}
                  isDragDisabled={!draggable} // 根据 draggable 属性控制是否禁用拖拽
                >
                  {(provided, snapshot) => (
                    <div
                      ref={provided.innerRef}
                      {...provided.draggableProps}
                      style={{
                        ...provided.draggableProps.style,
                        display: 'flex',
                        alignItems: 'center',
                        padding: '4px 0',
                        background: snapshot.isDragging ? '#e6f4ff' : 'transparent',
                        borderRadius: '4px',
                      }}
                    >
                      <Checkbox
                        checked={visibleColumns.includes(column.key)}
                        onChange={() => toggleColumn(column.key)}
                        disabled={column.columnsFix} // 固定列禁用勾选框
                        style={{ marginRight: '8px' }}
                      />
                      <span style={{ flex: 1 }}>{column.title}</span>
                      <div
                        {...provided.dragHandleProps}
                        style={{
                          // 固定列也显示拖拽图标
                          cursor: draggable ? 'grab' : 'not-allowed',
                          padding: '0 8px',
                          // color: column.columnsFix ? '#ccc' : 'inherit'
                          color: 'inherit',
                          opacity: draggable ? 1 : 0.5
                        }}
                      >
                        {draggable ? '≡' : ''}
                      </div>
                    </div>
                  )}
                </Draggable>
              ))}
              {provided.placeholder}
            </div>
          )}
        </Droppable>
      </DragDropContext>
    </Menu>
  );

  return (
    <Dropdown
      overlay={menu}
      visible={dropdownVisible}
      onVisibleChange={visible => setDropdownVisible(visible)}
      trigger={['click']}
      placement="bottomRight"
      align={{
        offset: [6, 12],
      }}
      overlayStyle={{ zIndex: 1001, position: 'fixed' }}
      getPopupContainer={() => document.body}
    >

      <Button
        type="text"
        icon={
          <img
            src={IconSet}
            style={{
              width: 20,
              height: 20,
              verticalAlign: 'middle',

            }}

          />
        }
        style={{

          background: 'transparent',

        }
        }

      />
    </Dropdown>
  );
};

export default ColumnSelector;