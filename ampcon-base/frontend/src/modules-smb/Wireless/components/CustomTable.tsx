import {
    Flex,
    Table,
} from "antd";
import { useTableInitialElement } from "@/modules-ampcon/hooks/useModalTable";
import React, { forwardRef, useEffect, useImperativeHandle, useMemo, useState, useCallback } from "react";
import { useDispatch } from "react-redux";
import { updateAlarmSearch, updateAlarmSearchStatus } from "@/store/modules/common/alarm_slice";
import dayjs from "dayjs";
import utc from "dayjs/plugin/utc";
import timezone from "dayjs/plugin/timezone";
import ColumnSelector from '@/modules-smb/Wireless/components/Modals/ColumnSelectorWithOrderProps';
import ResizableTable from '@/modules-smb/Wireless/components/Tables/ResizableTable';
// 引入useAuth hook用于访问preferences API
import { useAuth } from '@/modules-smb/contexts/AuthProvider';
import { useGetPreferences } from '@/modules-smb/hooks/Network/Account';
dayjs.extend(utc);
dayjs.extend(timezone);

export const createMatchMode = fields => {
    const matchModes = {};
    fields.forEach(field => {
        matchModes[field.name] = field.matchMode;
    });
    return matchModes;
};

export const WirelessCustomTable = forwardRef(
    (
        {
            quantity,
            columns,
            rowSelection,
            matchFieldsList,
            searchFieldsList,
            extraButton,
            helpDraw,
            fetchAPIInfo,
            fetchAPIParams,
            isShowPagination,
            disableInternalRowSelection,
            showColumnSelector = false,
            // 列排序配置
            columnsOrder = false,
            // 列宽拖拽
            resizableColumns = false,
            // 添加表格ID属性，用于preferences存储标识
            tableId,
            ...props
        },
        ref
    ) => {
        // 使用useAuth hook访问preferences API
        const { setPref } = useAuth();

        // 存储可见列的状态
        const [visibleColumns, setVisibleColumns] = useState([]);

        // 添加一个标志来防止重复初始化
        const [isInitialized, setIsInitialized] = useState(false);

        // 使用 useGetPreferences hook 直接获取 preferences 数据
        // 只有当需要列状态存储功能时才启用查询（移除列宽相关）
        const shouldEnablePreferences = !!(tableId && (columnsOrder || showColumnSelector));
        const { data: preferences, isLoading: preferencesLoading } = useGetPreferences({
            enabled: shouldEnablePreferences,
            // staleTime: 5 * 60 * 1000, // 5分钟内不重新获取
            // cacheTime: 10 * 60 * 1000, // 10分钟缓存时间
        });

        // 添加调试日志
        useEffect(() => {
            if (shouldEnablePreferences) {
                // console.log(`[WirelessCustomTable] tableId: ${tableId}, preferences loading: ${preferencesLoading}, preferences data:`, preferences);
            }
        }, [tableId, shouldEnablePreferences, preferencesLoading, preferences]);

        // 使用 useMemo 来稳定 columns 引用，避免不必要的重新初始化
        const stableColumns = useMemo(() => columns, [JSON.stringify(columns?.map(col => ({ key: col.key, dataIndex: col.dataIndex })))]);

        // 初始化列配置 - 仅在必要时执行（移除列宽相关逻辑）
        useEffect(() => {
            // 如果不需要 preferences 功能，直接初始化默认值
            if (!shouldEnablePreferences) {
                if (!isInitialized && stableColumns) {
                    const defaultVisibleColumns = stableColumns.map(col => col.key || col.dataIndex);
                    setVisibleColumns(defaultVisibleColumns);
                    setIsInitialized(true);
                }
                return;
            }

            // 需要 preferences 功能时，等待数据加载完成
            if (!tableId || !stableColumns || isInitialized || preferencesLoading) return;

            // 如果 preferences 为 null 或 undefined，使用默认值
            if (!preferences) {
                const defaultVisibleColumns = stableColumns.map(col => col.key || col.dataIndex);
                setVisibleColumns(defaultVisibleColumns);
                setIsInitialized(true);
                return;
            }

            let newVisibleColumns = [];

            // 直接在 useEffect 内部定义 getPref 函数，避免依赖外部函数
            const getPreference = (preference) => {
                for (const pref of preferences) {
                    if (pref.tag === preference) return pref.value;
                }
                return null;
            };

            try {
                const savedVisibleColumns = getPreference('table_' + tableId + '_visible_columns');
                const savedColumnOrder = getPreference('table_' + tableId + '_column_order');

                if (savedVisibleColumns) {
                    newVisibleColumns = JSON.parse(savedVisibleColumns);
                } else if (savedColumnOrder) {
                    // 如果没有保存的可见列，但有列顺序，使用列顺序作为可见列
                    newVisibleColumns = JSON.parse(savedColumnOrder);
                } else {
                    // 默认显示所有列
                    newVisibleColumns = stableColumns.map(col => col.key || col.dataIndex);
                }

                // 如果有保存的列顺序，按照该顺序重新排列可见列
                if (savedColumnOrder) {
                    const columnOrder = JSON.parse(savedColumnOrder);
                    // 按照保存的顺序重新排列 newVisibleColumns
                    const orderedVisibleColumns = [];

                    // 首先添加按顺序排列的列
                    columnOrder.forEach(key => {
                        if (newVisibleColumns.includes(key)) {
                            orderedVisibleColumns.push(key);
                        }
                    });

                    // 然后添加不在顺序中但可见的列
                    newVisibleColumns.forEach(key => {
                        if (!orderedVisibleColumns.includes(key)) {
                            orderedVisibleColumns.push(key);
                        }
                    });

                    newVisibleColumns = orderedVisibleColumns;
                }
            } catch (e) {
                // console.error('Error parsing column preferences:', e);
                // 出错时使用默认值
                newVisibleColumns = stableColumns.map(col => col.key || col.dataIndex);
            }

            // console.log('[WirelessCustomTable] Initializing with saved data:', {
            //     visibleColumns: newVisibleColumns,
            //     hasColumnOrder: !!getPreference('table_' + tableId + '_column_order'),
            //     tableId
            // });

            setVisibleColumns(newVisibleColumns);
            setIsInitialized(true);
        }, [tableId, stableColumns, preferences, preferencesLoading, isInitialized, shouldEnablePreferences]); // 添加所有相关依赖

        // 根据visibleColumns来过滤和排序列
        const filteredColumns = useMemo(() => {
            if (!stableColumns) return [];

            // 筛选可见列
            const visibleKeys = visibleColumns.length > 0 ? visibleColumns : stableColumns.map(col => col.key || col.dataIndex);

            // 过滤出可见的列
            const visibleColumnsData = stableColumns.filter(col => {
                const key = col.key || col.dataIndex;
                return visibleKeys.includes(key);
            });

            // 按照 visibleColumns 的顺序排序
            return visibleColumnsData.sort((a, b) => {
                const keyA = a.key || a.dataIndex;
                const keyB = b.key || b.dataIndex;
                const indexA = visibleKeys.indexOf(keyA);
                const indexB = visibleKeys.indexOf(keyB);
                return indexA - indexB;
            });
        }, [stableColumns, visibleColumns]);

        // 保存列可见性配置
        const saveVisibleColumns = useCallback(async (newVisibleColumns) => {
            if (!tableId) return;

            try {
                setVisibleColumns(newVisibleColumns);
                await setPref({
                    preference: 'table_' + tableId + '_visible_columns',
                    value: JSON.stringify(newVisibleColumns)
                });
            } catch (e) {
                // console.error('Error saving visible columns:', e);
            }
        }, [tableId, setPref]);

        // 保存列顺序配置（移除列宽相关逻辑）
        const saveColumnOrder = useCallback(async (newColumnOrder) => {
            if (!tableId) return;

            try {
                await setPref({
                    preference: 'table_' + tableId + '_column_order',
                    value: JSON.stringify(newColumnOrder)
                });
            } catch (e) {
                // console.error('Error saving column order:', e);
            }
        }, [tableId, setPref]);

        const handleVisibleColumnsChange = useCallback((newVisibleColumns) => {
            const fixedKeys = stableColumns.filter(col => col.columnsFix).map(col => col.key || col.dataIndex);
            const merged = Array.from(new Set([...newVisibleColumns, ...fixedKeys]));

            // 保存可见列配置
            saveVisibleColumns(merged);
        }, [stableColumns, saveVisibleColumns]);

        // 处理列顺序变化
        const handleColumnOrderChange = useCallback((newColumnOrder) => {
            saveColumnOrder(newColumnOrder);
        }, [saveColumnOrder]);

        // 为所有列添加resizable属性（移除列宽持久化逻辑）
        const processedColumns = useMemo(() => {
            if (!resizableColumns) {
                return filteredColumns;
            }

            return filteredColumns.map(column => ({
                ...column,
                // 只有当列没有明确设置 resizable: false 时才启用拖拽调整
                ...(column.resizable !== false ? { resizable: true } : {}),
                // 保持原有的列宽，不强制设置默认宽度
                // 确保有最小宽度限制
                ...(column.resizable !== false ? { minWidth: column.minWidth || 80 } : {})
            }));
        }, [filteredColumns, resizableColumns]);

        const finalColumns = useMemo(() => {
            let cols = showColumnSelector
                ? [
                    ...processedColumns,
                    {
                        title: (
                            <div>
                                <ColumnSelector
                                    columns={columns}
                                    visibleColumns={visibleColumns}
                                    onChange={handleVisibleColumnsChange}
                                    draggable={columnsOrder} // 将 columnsOrder 作为 draggable 属性传递
                                    onDragEnd={handleColumnOrderChange} // 添加拖拽结束事件处理

                                />
                            </div>
                        ),
                        key: 'columnSelector',
                        width: '44px',
                        fixed: 'right',
                        resizable: false, // 明确禁用列选择器列的调整功能
                        // 表头单元格样式 - 关键优化
                        onHeaderCell: () => ({
                            style: {
                                width: '44px',
                                padding: 0,
                                height: '54px', // 与内容行高保持一致
                                display: 'flex',
                                alignItems: 'center',
                                justifyContent: 'center',

                            }
                        }),

                    }
                ]
                : processedColumns;
            return cols;
        }, [columns, showColumnSelector, visibleColumns, handleVisibleColumnsChange, processedColumns, columnsOrder]);


        const [_, __, searchFields, setSearchFields, data, setData, loading, setLoading, pagination, setPagination] =
            useTableInitialElement(searchFieldsList, false);

        const matchModes = createMatchMode(matchFieldsList || []);

        const [tableSelectedRowKey, setTableSelectedRowKey] = useState(
            rowSelection ? rowSelection.selectedRowKeys : []
        );
        const [tableSelectedRows, setTableSelectedRows] = useState(rowSelection ? rowSelection.selectedRows : []);
        const [tableRemovedRowKey, setTableRemovedRowKey] = useState([]);
        const [tableRemovedRows, setTableRemovedRows] = useState([]);
        const [operations, setOperations] = useState({});
        const [operationRowsMappings, setOperationRowsMappings] = useState({});
        const dispatch = useDispatch();

        const [sorter, setSorter] = useState({});
        const checkSortedColumn = columns => {
            for (const columnKey in columns) {
                if (Object.prototype.hasOwnProperty.call(columns, columnKey)) {
                    const columnConfig = columns[columnKey];
                    // check each column has defaultSortOrder or not
                    if (columnConfig.defaultSortOrder !== null) {
                        return [columnConfig.dataIndex, columnConfig.defaultSortOrder];
                    }
                }
            }
            return [undefined, undefined];
        };
        const [filters, setFilters] = useState({});

        const handleSelect = (record, selected) => {
            const keys = selected
                ? tableSelectedRowKey.concat([record.id])
                : tableSelectedRowKey.filter(item => item !== record.id);

            if (!selected) {
                setTableRemovedRowKey([...tableRemovedRowKey, record.id]);
                setTableRemovedRows([...tableRemovedRows, record]);
            } else {
                setTableRemovedRowKey(tableRemovedRowKey.filter(item => item !== record.id));
                setTableRemovedRows(tableRemovedRows.filter(item => item.id !== record.id));
            }

            const rows = selected
                ? [...tableSelectedRows, record]
                : tableSelectedRows.filter(item => item.id !== record.id);

            if (Object.prototype.hasOwnProperty.call(record, "selected")) {
                const operationsTemp = operations;
                if (record.selected === false && selected) {
                    operationsTemp[record.id] = "add";
                } else if (record.selected === false && !selected) {
                    delete operationsTemp[record.id];
                } else if (record.selected === true && selected) {
                    delete operationsTemp[record.id];
                } else if (record.selected === true && !selected) {
                    operationsTemp[record.id] = "remove";
                }
                setOperations(operationsTemp);
            }

            rows.forEach(row => {
                if (row.children) {
                    const isInChildren = row.children.some(child => child.id === record.id);
                    if (isInChildren) {
                        const rowIndex = rows.findIndex(r => r.id === row.id);
                        if (rowIndex > -1) {
                            rows.splice(rowIndex, 1);
                        }

                        const keyIndex = keys.findIndex(k => k === row.id);
                        if (keyIndex > -1) {
                            keys.splice(keyIndex, 1);
                        }
                    }
                }
            });

            if (Object.prototype.hasOwnProperty.call(record, "children")) {
                if (selected) {
                    record.children.forEach(child => {
                        if (!keys.includes(child.id)) {
                            keys.push(child.id);
                        }
                        if (!rows.some(row => row.id === child.id)) {
                            rows.push(child);
                        }
                    });
                } else {
                    const needRemoveRows = record.children.map(child => child.id);
                    needRemoveRows.forEach(id => {
                        const rowIndex = rows.findIndex(r => r.id === id);
                        rows.splice(rowIndex, 1);
                        const keyIndex = keys.findIndex(k => k === id);
                        keys.splice(keyIndex, 1);
                    });
                }
            }
            setTableSelectedRowKey(keys);
            setTableSelectedRows(rows);

            if (rowSelection && rowSelection.onChange) {
                rowSelection.onChange(keys, rows);
            }
        };

        const handleSelectAll = (selected, selectedRows, changeRows) => {
            if (quantity) {
                const currentCount = tableSelectedRowKey.length;
                const remaining = quantity - currentCount;

                if (selected && remaining <= 0) {
                    return; // 已满，不允许再选
                }

                const limitedRows = selected ? changeRows.slice(0, remaining) : changeRows;
                const limitedIds = limitedRows.map(item => item.id);

                const keys = selected
                    ? Array.from(new Set([...tableSelectedRowKey, ...limitedIds]))
                    : tableSelectedRowKey.filter(item => !limitedIds.includes(item));
                setTableSelectedRowKey(keys);

                const rows = selected
                    ? Array.from(new Set([...tableSelectedRows, ...limitedRows]))
                    : tableSelectedRows.filter(item => !limitedIds.includes(item.id));
                setTableSelectedRows(rows);

                if (!selected) {
                    setTableRemovedRowKey([...tableRemovedRowKey, ...limitedIds]);
                    setTableRemovedRows([...tableRemovedRows, ...limitedRows]);
                } else {
                    setTableRemovedRowKey(tableRemovedRowKey.filter(item => !limitedIds.includes(item)));
                    setTableRemovedRows(tableRemovedRows.filter(item => !ids.includes(item.id)));
                }

                if (Object.prototype.hasOwnProperty.call(changeRows[0], "selected")) {
                    const operationsTemp = { ...operations };
                    limitedRows.forEach(record => {
                        if (record.selected === false && selected) {
                            operationsTemp[record.id] = "add";
                        } else if (record.selected === false && !selected) {
                            delete operationsTemp[record.id];
                        } else if (record.selected === true && selected) {
                            delete operationsTemp[record.id];
                        } else if (record.selected === true && !selected) {
                            operationsTemp[record.id] = "remove";
                        }
                    });
                    setOperations(operationsTemp);
                }

                if (rowSelection && rowSelection.onChange) {
                    rowSelection.onChange(keys, rows);
                }
            } else {
                const ids = changeRows.map(item => item.id);
                const keys = selected
                    ? tableSelectedRowKey.concat(ids)
                    : tableSelectedRowKey.filter(item => !ids.includes(item));
                setTableSelectedRowKey(keys);

                if (!selected) {
                    setTableRemovedRowKey([...tableRemovedRowKey, ...ids]);
                    setTableRemovedRows([...tableRemovedRows, ...changeRows]);
                } else {
                    setTableRemovedRowKey(tableRemovedRowKey.filter(item => !ids.includes(item)));
                    setTableRemovedRows(tableRemovedRows.filter(item => !ids.includes(item.id)));
                }
                const rows = selected
                    ? [...tableSelectedRows, ...changeRows]
                    : tableSelectedRows.filter(item => !ids.includes(item.id));
                setTableSelectedRows(rows);

                // for default selected rows
                if (Object.prototype.hasOwnProperty.call(changeRows[0], "selected")) {
                    changeRows.map(record => {
                        const operationsTemp = operations;
                        if (record.selected === false && selected) {
                            operationsTemp[record.id] = "add";
                        } else if (record.selected === false && !selected) {
                            delete operationsTemp[record.id];
                        } else if (record.selected === true && selected) {
                            delete operationsTemp[record.id];
                        } else if (record.selected === true && !selected) {
                            operationsTemp[record.id] = "remove";
                        }
                        setOperations(operationsTemp);
                        setOperationRowsMappings(prev => {
                            const newMappings = { ...prev };
                            newMappings[record.id] = record;
                            return newMappings;
                        });
                    });
                }

                if (rowSelection && rowSelection.onChange) {
                    rowSelection.onChange(keys, rows);
                }
            }
        };

        const tableRowSelection = useMemo(() => {
            if (rowSelection?.type === "radio") {
                return rowSelection;
            }

            if (disableInternalRowSelection && rowSelection) {
                return rowSelection;
            }

            return {
                selectedRowKeys: tableSelectedRowKey,
                onSelect: handleSelect,
                onSelectAll: handleSelectAll,
                getCheckboxProps: rowSelection?.getCheckboxProps,
                fixed: rowSelection?.fixed,
                checkStrictly: rowSelection?.checkStrictly
            };
        }, [
            disableInternalRowSelection,
            rowSelection?.type,
            rowSelection?.getCheckboxProps,
            rowSelection?.fixed,
            rowSelection?.checkStrictly,
            tableSelectedRowKey,
            handleSelect,
            handleSelectAll
        ]);

        useImperativeHandle(ref, () => ({
            refreshTable() {
                fetchData().then();
            },
            setTableLoading(value) {
                setLoading(value);
            },
            getSelectedRow: () => {
                return { tableSelectedRowKey, tableSelectedRows };
            },
            getRemovedRow: () => {
                return { tableRemovedRowKey, tableRemovedRows };
            },
            clearSelectedRow: () => {
                setTableSelectedRowKey([]);
                setTableSelectedRows([]);
            },
            getOperations: () => {
                return operations;
            },
            getOperationRowsMappings: () => {
                return operationRowsMappings;
            },
            getTableData: () => {
                return data;
            },
            clearAndRefresh: () => {
                setTableSelectedRowKey([]);
                setTableSelectedRows([]);
                fetchData(true);
            },
            refreshAndSaveSelectedRow: () => {
                fetchData(true);
            }
        }));

        const fetchData = async (ignoreSelection = false) => {
            setLoading(true);
            const filterFields = filters ? createFilterFields(filters, matchModes) : [];
            const sortFields = [];
            if (sorter.field && sorter.order) {
                sortFields.push({
                    field: sorter.field,
                    order: sorter.order === "ascend" ? "asc" : "desc"
                });
            }

            try {
                let response = await fetchAPIInfo(
                    ...(fetchAPIParams
                        ? [
                            ...fetchAPIParams,
                            pagination.current,
                            pagination.pageSize,
                            filterFields,
                            sortFields,
                            searchFields
                        ]
                        : [pagination.current, pagination.pageSize, filterFields, sortFields, searchFields])
                );
                if (response.data.length === 0 && response.total !== 0) {
                    response = await fetchAPIInfo(
                        ...(fetchAPIParams
                            ? [
                                ...fetchAPIParams,
                                Math.ceil(response.total / response.pageSize),
                                pagination.pageSize,
                                [],
                                [],
                                searchFields
                            ]
                            : [
                                Math.ceil(response.total / response.pageSize),
                                pagination.pageSize,
                                [],
                                [],
                                searchFields
                            ])
                    );
                }

                const responseDataTemp = JSON.parse(JSON.stringify(response.data));
                if (!ignoreSelection) {
                    responseDataTemp.forEach(item => {
                        item.selected = operations[item.id] === "add" ? true : item.selected;
                    });

                    if (responseDataTemp.every(item => "selected" in item)) {
                        const backendSelectedRowKeys = responseDataTemp
                            .filter(item => item.selected)
                            .map(item => item.id);
                        const frontendSelectedRowKeys = tableSelectedRowKey
                            ? responseDataTemp
                                .filter(item => tableSelectedRowKey.indexOf(item.id) > -1)
                                .map(item => item.id)
                            : [];
                        const removedRowKeys = tableRemovedRowKey
                            ? responseDataTemp
                                .filter(item => tableRemovedRowKey.indexOf(item.id) > -1)
                                .map(item => item.id)
                            : [];
                        const selectedRowKeys = Array.from(
                            new Set([
                                ...(tableSelectedRowKey || []),
                                ...backendSelectedRowKeys,
                                ...frontendSelectedRowKeys
                            ])
                        ).filter(itemId => removedRowKeys.indexOf(itemId) === -1);
                        setTableSelectedRowKey(selectedRowKeys);
                        setTableSelectedRows(responseDataTemp.filter(item => item.selected));
                    }
                }

                setData(responseDataTemp);
                setPagination(prev => ({
                    ...prev,
                    total: response.total,
                    current: response.page,
                    pageSize: response.pageSize
                }));
            } catch (error) {
                // console.log("error", error);
            } finally {
                setLoading(false);
            }
        };

        useEffect(() => {
            const [sortedColumn, sortedOrder] = checkSortedColumn(columns);
            if (sortedColumn) {
                sorter.field = sortedColumn;
                sorter.order = sortedOrder;
                // tableChange("", "", sorter);
            }
        }, []);

        useEffect(() => {
            // if (!props.readTag)
            fetchData().then();
        }, [JSON.stringify(fetchAPIParams), JSON.stringify(searchFields)]);

        const handleSearchChange = e => {
            dispatch(updateAlarmSearchStatus(false));
            dispatch(updateAlarmSearch(""));
            if (!preSavedSelectedRowKeys) {
                if (tableRowSelection && tableRowSelection.selectedRowKeys) {
                    tableRowSelection.selectedRowKeys = [];
                }
                setTableSelectedRows([]);
                setTableSelectedRowKey([]);
            }

            setSearchFields({
                fields: searchFieldsList,
                value: e.target.value
            });
        };

        const tableChange = async (pagination, filters, sorter) => {
            const delay = ms =>
                new Promise(resolve => {
                    setTimeout(resolve, ms);
                });
            await delay(100);
            setSorter(sorter);
            setFilters(filters);
            await handleTableChange(
                pagination,
                filters,
                sorter,
                setPagination,
                searchFields,
                fetchAPIInfo,
                fetchAPIParams,
                setData,
                matchModes,
                setLoading,
                tableSelectedRowKey,
                tableSelectedRows,
                setTableSelectedRowKey,
                setTableSelectedRows,
                tableRemovedRowKey
            );
        };



        return (
            <div >
                <Flex vertical>
                    <Flex gap="middle" style={{ marginBottom: "20px" }}>
                        {extraButton}
                        <div style={{ flexGrow: 1 }} />
                        {searchFieldsList ? <GlobalSearchInput onChange={handleSearchChange} /> : null}
                        {helpDraw}
                    </Flex>
                    {
                        (() => {
                            const TableComponent = resizableColumns ? ResizableTable : Table;

                            const tableProps = {
                                rowSelection: rowSelection ? tableRowSelection : null,
                                columns: finalColumns,
                                bordered: true,
                                rowKey: record => record.id,
                                loading,
                                dataSource: data,
                                pagination: searchFieldsList || isShowPagination ? pagination : false,
                                onChange: tableChange,
                                scroll: { x: 'max-content' },
                                ...(resizableColumns ? {
                                    resizableColumns: true
                                } : {}),
                                ...props
                            };

                            return (
                                <TableComponent {...tableProps} />
                            );
                        })()
                    }
                </Flex>
            </div>
        );
    }
);
