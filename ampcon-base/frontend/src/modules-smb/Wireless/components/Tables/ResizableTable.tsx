import React, { useState, useCallback, useMemo, useEffect } from 'react';
import { Table } from 'antd';
import { Resizable } from 'react-resizable';
import 'react-resizable/css/styles.css';
import './ResizableTable.css';

// 可调整大小的表头组件
const ResizableTitle = (props) => {
  const { onResize, width, ...restProps } = props;

  if (!width) {
    return <th {...restProps} />;
  }

  return (
    <Resizable
      width={width}
      height={0}
      handle={
        <span
          className="react-resizable-handle"
          onClick={(e) => {
            e.stopPropagation();
          }}
          onMouseDown={(e) => {
            // 确保只有在调整列宽时才阻止事件传播
            e.stopPropagation();
          }}
        />
      }
      onResize={onResize}
      draggableOpts={{ enableUserSelectHack: false }}
    >
      <th {...restProps} />
    </Resizable>
  );
};

// 可调整列宽的表格组件
const ResizableTable = ({ columns: initialColumns, resizableColumns = false, ...props }) => {
  const [columns, setColumns] = useState(() => {
    if (!resizableColumns) return initialColumns;

    return initialColumns.map((col) => ({
      ...col,
      // width: col.width,
      width: col.width || 100,
      minWidth: col.minWidth || 80,
    }));
  });

  // 当外部列发生变化时，同步更新内部状态
  useEffect(() => {
    if (!resizableColumns) {
      setColumns(initialColumns);
      return;
    }

    // 保持已调整的宽度，但更新列的顺序和其他属性
    const updatedColumns = initialColumns.map((newCol) => {
      const existingCol = columns.find(col => col.key === newCol.key);
      return {
        ...newCol,
        width: existingCol?.width || newCol.width, // 保持原有宽度，不设置默认值
        minWidth: newCol.minWidth || 80,
      };
    });

    setColumns(updatedColumns);
  }, [initialColumns, resizableColumns]);

  const handleResize = useCallback((index) => (e, { size }) => {
    setColumns((prevColumns) => {
      const nextColumns = [...prevColumns];
      const minWidth = nextColumns[index].minWidth || 80;
      const newWidth = Math.max(size.width, minWidth);

      // 只有当宽度真正改变时才更新
      if (nextColumns[index].width !== newWidth) {
        nextColumns[index] = {
          ...nextColumns[index],
          width: newWidth,
        };
      }

      return nextColumns;
    });
  }, []);

  const mergedColumns = useMemo(() => {
    if (!resizableColumns) return initialColumns;

    return columns.map((col, index) => {
      // 如果列明确设置了 resizable: false，则不添加调整功能
      if (col.resizable === false) {
        return col;
      }

      return {
        ...col,
        onHeaderCell: (column) => ({
          width: column.width,
          onResize: handleResize(index),
          ...(col.onHeaderCell ? col.onHeaderCell(column) : {}),
        }),
      };
    });
  }, [columns, handleResize, resizableColumns]);

  const components = useMemo(() => {
    if (!resizableColumns) return undefined;

    return {
      header: {
        cell: ResizableTitle,
      },
    };
  }, [resizableColumns]);

  return (
    <Table
      {...props}
      columns={mergedColumns}
      components={components}
      className={`${props.className || ''} ${resizableColumns ? 'resizable-table' : ''}`}
    />
  );
};

export default ResizableTable;
