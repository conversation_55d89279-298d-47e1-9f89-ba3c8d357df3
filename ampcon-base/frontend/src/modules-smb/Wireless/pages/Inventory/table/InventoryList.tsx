import React, { useCallback, useState, forwardRef, useImperativeHandle, useEffect } from 'react';
import { useDisclosure } from '@chakra-ui/react';
import { Card, Form, Switch, Space, message, Button } from 'antd';
import { CellContext } from '@tanstack/react-table';
import { useTranslation } from 'react-i18next';
import { SortInfo } from '@/modules-smb/models/Table';
import { v4 as uuid } from 'uuid';
import Actions from './Actions';
// import { WirelessCustomTable } from '@/modules-ampcon/components/custom_table';
import { WirelessCustomTable } from '@/modules-smb/Wireless/components/CustomTable';
// import ExportDevicesTableButton from '@/modules-smb/components/ExportInventoryButton';
import ExportDevicesTableButtonAntd from '@/modules-smb/Wireless/components/Button/ExportInventoryButton/indexAntd';
import FormattedDate from '@/modules-smb/components/FormattedDate';
import FactoryResetModal from '@/modules-smb/Wireless/components/Modals/SubscriberDevice/FactoryResetModal';
// import WifiScanModal from '@/modules-smb/Wireless/components/Modals/SubscriberDevice/WifiScanModal';
import DeviceSearchBar from '@/modules-smb/Wireless/components/SearchBar/DeviceSearch';
import VenueCell from '@/modules-smb/Wireless/components/TableCells/VenueCell';
import ConfigurationPushModal from '@/modules-smb/components/Tables/InventoryTable/ConfigurationPushModal';
import CreateConfigurationModal from '@/modules-smb/Wireless/components/Tables/InventoryTable/CreateTagModal';
// import EditTagModal from '@/modules-smb/components/Tables/InveWireless/ntoryTable/EditTagModal';
import EditForms from './EditForms';
import {
  useGetInventoryCount,
  useGetInventoryTags,
  usePushConfig,
} from '@/modules-smb/hooks/Network/Inventory';
import { Device } from '@/modules-smb/models/Device';
import { InventoryTagApiResponse } from '@/modules-smb/models/Inventory';
import ImportDeviceCsvModalAntd from '@/modules-smb/Wireless/components/Button/ImportDeviceCsvModal/indexAntd';
import { refreshSvg, deleteSvg } from "@/utils/common/iconSvg";
import Icon from "@ant-design/icons";
import { confirmModalAction } from "@/modules-ampcon/components/custom_modal";
import useGetEntityFavorite from "@/modules-smb/Wireless/hooks/useGetEntityFavorite";
import { batchDeleteInventory } from "@/modules-smb/Wireless/apis/inventory_api.jsx";
type InventoryTableProps = {
  venueId?: string;
  onlyUnassigned: boolean;
};
interface PageInfo {
  index: number;
  limit: number;
}
const InventoryTable = forwardRef(({ venueId, onlyUnassigned }: InventoryTableProps, ref) => {

  const { defaultValue } = useGetEntityFavorite();
  let hashVenueId: string | null = null;
  const hash = window.location.hash.replace('#', '');
  if (hash && /^\d+$/.test(hash)) {
    hashVenueId = hash;
  }

  let validDefaultValue = null;
  if (defaultValue && /^\d+$/.test(defaultValue)) {
    validDefaultValue = defaultValue;
  }

  venueId = venueId || hashVenueId || validDefaultValue;
  const { t } = useTranslation();
  const [serialNumber, setSerialNumber] = useState<string>('');
  const [tag, setTag] = useState<Device | { serialNumber: string } | undefined>(undefined);
  const { isOpen: isEditOpen, onOpen: openEdit, onClose: closeEdit } = useDisclosure();
  const { isOpen: isPushOpen, onOpen: openPush, onClose: closePush } = useDisclosure();
  const [isDeleting, setIsDeleting] = useState(false);
  const [sortInfo, setSortInfo] = useState<SortInfo>([{ id: 'serialNumber', sort: 'asc' }]);
  const [pageInfo, setPageInfo] = useState<PageInfo>({ index: 0, limit: 10 });
  const [selectedRowKeys, setSelectedRowKeys] = useState<string[]>([]);
  const [selectedRows, setSelectedRows] = useState([]);
  const scanModalProps = useDisclosure();
  const resetModalProps = useDisclosure();
  const upgradeModalProps = useDisclosure();
  const pushConfiguration = usePushConfig({ onSuccess: () => openPush() });
  const {
    data: count,
    isFetching: isFetchingCount,
    refetch: refetchCount,
  } = useGetInventoryCount({
    enabled: true,
    onlyUnassigned,
    venueId: venueId
  });

  const {
    data: tags,
    isFetching: isFetchingTags,
    refetch: refetchTags,
  } = useGetInventoryTags({
    pageInfo: pageInfo,
    sortInfo,
    enabled: true,
    count,
    onlyUnassigned,
    venueId: venueId
  });
  const rowSelection = {
    selectedRowKeys,
    selectedRows,
    onChange: (keys, rows) => {
      setSelectedRowKeys(keys);
      setSelectedRows(rows);
    },
  };


  const deleteSelected = async (selectedRowKeys) => {
    if (!selectedRowKeys.length) return;

    setIsDeleting(true);
    try {
      const idList = selectedRowKeys;
      await batchDeleteInventory(idList);
      message.success('Batch delete inventory success.');
      refetchCount();
      refetchTags();
      setSelectedRowKeys([]);
    } catch (error) {
      message.error(`Failed to delete devices: ${error?.response?.data?.ErrorDescription || 'Unknown error'}`);
    } finally {
      setIsDeleting(false);
    }
  };

  const serialCell = React.useCallback(
    (device) => {
      return (<a href={`/wireless/devices/${device.serialNumber}#/devices/${device.serialNumber}`} style={{ color: '#14C9BB ', textDecoration: 'underline' }} >
        <pre>{device.serialNumber}</pre>
      </a>
      );
    },
    [],);


  // 新增：删除确认逻辑（使用 confirmModalAction）
  const handleDeleteSelected = () => {
    confirmModalAction(
      `Are you sure you need to delete the selected device?`,
      () => {
        deleteSelected(selectedRowKeys); // 执行删除
      }
    );
  };

  const handleTableChange = (pagination, filters, sorter) => {
    console.log("pagination", pagination)
    setPageInfo({
      index: pagination.current - 1,
      limit: pagination.pageSize,
    });
    if (sorter.field) {
      setSortInfo([{
        id: sorter.field,
        sort: sorter.order === 'ascend' ? 'asc' : 'desc'
      }]);
    }
  };

  const onOpenScan = (serial: string) => {
    setSerialNumber(serial);
    scanModalProps.onOpen();
  };

  const onOpenFactoryReset = (serial: string) => {
    setSerialNumber(serial);
    resetModalProps.onOpen();
  };

  const onOpenUpgradeModal = (serial: string) => {
    setSerialNumber(serial);
    upgradeModalProps.onOpen();
  };

  const openEditModal = (newTag: Device | { serialNumber: string }) => {
    setTag(newTag);
    openEdit();
  };

  const memoizedActions = useCallback(
    (cell: CellContext<InventoryTagApiResponse, unknown>) => (
      <Actions
        cell={cell.row as unknown as { original: Device }}
        refreshTable={refetchCount}
        key={uuid()}
        openEditModal={openEditModal}
        // onOpenScan={onOpenScan}
        onOpenFactoryReset={onOpenFactoryReset}
        onOpenUpgradeModal={onOpenUpgradeModal}
      />
    ),
    [],
  );

  const memoizedDate = useCallback(
    (cell: CellContext<InventoryTagApiResponse, unknown>, key: 'modified') => (
      <FormattedDate date={cell.row.original[key]} key={uuid()} />
    ),
    [],
  );

  const venueCell = useCallback(
    (cell: CellContext<InventoryTagApiResponse, unknown>) => (
      <VenueCell venueName={cell.row.original.extendedInfo?.venue?.name ?? ''} venueId={cell.row.original.venue} />
    ),
    [],
  );

  const onSearchClick = useCallback((serial: string) => {
    openEditModal({ serialNumber: serial });
  }, []);

  const columns = React.useMemo(() => {
    const baseColumns = [
      {
        key: 'serialNumber',
        title: (
          <span style={{ whiteSpace: 'nowrap' }}>
            {t('inventory.serial_number')}
          </span>
        ),
        dataIndex: 'serialNumber',
        render: (_, record) => serialCell(record),
        sorter: true,
        columnsFix: true,
      },
      {
        key: 'name',
        title: t('common.name'),
        dataIndex: 'name',
        sorter: true,
      },
      {
        key: 'venue',
        title: t('inventory.site'),
        dataIndex: ['venue'],
        render: (_, record) => venueCell({ row: { original: record } }),
        sorter: true,
      },
      {
        key: 'description',
        title: t('common.description'),
        dataIndex: 'description',
        sorter: true,
      },
      {
        key: 'label',
        title: t('inventory.label'),
        dataIndex: 'labelsName',
        sorter: true,
      },
      {
        key: 'modified',
        title: t('common.modified'),
        dataIndex: 'modified',
        render: (_, record) => memoizedDate({ row: { original: record } }, 'modified'),
        sorter: true,
      },
      {
        key: 'operation',
        title: 'Operation',
        dataIndex: 'operation',
        render: (_, record) => memoizedActions({ row: { original: record } }, 'operation'),
        columnsFix: true,
      },
    ];

    return baseColumns;
  }, [t]);

  const handleRefresh = () => {
    refetchCount();
    refetchTags();
  };

  useImperativeHandle(ref, () => ({
    refreshTable: () => {
      refetchCount();
      refetchTags();
    },
  }));

  useEffect(() => {
    setPageInfo(prevPageInfo => ({
      ...prevPageInfo,
      index: 0
    }));
    setSelectedRowKeys([]);
    setSelectedRows([]);
  }, [venueId, onlyUnassigned]);

  return (
    <>
      <div style={{
        display: 'flex',
        justifyContent: 'space-between',  // 左右两端对齐
        alignItems: 'center',
      }}>
        <div style={{ display: 'flex', alignItems: 'center' }}>
          <Space size={16} align="middle">
            <CreateConfigurationModal refresh={refetchCount} venueId={venueId} />
            <ExportDevicesTableButtonAntd />
            <ImportDeviceCsvModalAntd refresh={refetchCount} handleRefresh={handleRefresh} deviceClass="venue" />
            <Button
              htmlType="button"
              style={{ display: "flex", alignItems: "center" }}
              onClick={() => {
                handleRefresh();
                message.success("Inventory table refresh success.");
              }}
              icon={<Icon component={refreshSvg} />}
            >
              Refresh
            </Button>
            <Button
              htmlType="button"
              style={{ display: "flex", alignItems: "center" }}
              onClick={handleDeleteSelected}
              disabled={selectedRowKeys.length === 0 || isDeleting}
              loading={isDeleting}
              icon={
                <Icon
                  component={deleteSvg}
                  style={{
                    // 当按钮禁用时，使用滤镜将图标转为灰色
                    filter: (selectedRowKeys.length === 0 || isDeleting) ? 'grayscale(100%)' : 'none',
                  }}
                />
              }
            >
              Delete
            </Button>
          </Space>
        </div>

        <div style={{ minWidth: '280px' }}>
          <DeviceSearchBar onClick={onSearchClick} />
        </div>
      </div>
      {/* <Card bordered={false}> */}
      <WirelessCustomTable
        columnsOrder={true}
        resizableColumns={true}
        tableId='devices-table'
        ref={ref}
        columns={onlyUnassigned ? columns.filter(col => col.key !== 'entity' && col.key !== 'venue') : columns}
        dataSource={tags || []}
        loading={isFetchingCount || isFetchingTags}
        onChange={handleTableChange}
        showColumnSelector='true'
        rowSelection={rowSelection}
        disableInternalRowSelection
        pagination={{
          current: pageInfo.index + 1,
          pageSize: pageInfo.limit,
          total: count,
          showSizeChanger: true,
          showQuickJumper: true,
          showTotal: (total) => `Total ${total} items`,
          pageSizeOptions: ['10', '20', '50', '100'],
        }}
      />
      {/* <EditTagModal
        isOpen={isEditOpen}
        onClose={closeEdit}
        tag={tag}
        refresh={refetchTags}
        pushConfig={pushConfiguration}
        onOpenScan={onOpenScan}
        onOpenFactoryReset={onOpenFactoryReset}
        onOpenUpgradeModal={onOpenUpgradeModal}
      /> */}
      <EditForms
        tag={tag}
        key={tag?.serialNumber}
        refresh={refetchTags}
        onClose={closeEdit}
        open={isEditOpen}
      />
      <ConfigurationPushModal isOpen={isPushOpen} onClose={closePush} pushResult={pushConfiguration.data} />
      {/* <WifiScanModal modalProps={scanModalProps} serialNumber={serialNumber} />  */}
      <FactoryResetModal modalProps={resetModalProps} serialNumber={serialNumber} />
      {/* </Card> */}
    </>
  );
});

export default InventoryTable;