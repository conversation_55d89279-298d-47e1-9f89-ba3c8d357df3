# 列顺序恢复和列宽一致性问题修复指南

## 🔍 问题分析

### 问题1: 列顺序没有正确恢复
**原因**: 在初始化时只加载了 `visible_columns`，没有加载 `column_order`，导致页面刷新后列顺序重置。

### 问题2: resizableColumns=true 时列宽不一致
**原因**: 在 `processedColumns` 中，当列没有设置 `width` 时，强制设置为 200px，导致列宽与原来不一致。

## 🔧 修复方案

### 1. 修复列顺序恢复逻辑
**文件**: `ampcon-base/frontend/src/modules-ampcon/components/custom_table.jsx`

**主要修改**:
```jsx
// 初始化时同时加载可见列和列顺序
const savedVisibleColumns = getPreference('table_' + tableId + '_visible_columns');
const savedColumnOrder = getPreference('table_' + tableId + '_column_order');

// 如果有保存的列顺序，按照该顺序重新排列可见列
if (savedColumnOrder) {
  const columnOrder = JSON.parse(savedColumnOrder);
  // 按照保存的顺序重新排列 newVisibleColumns
  const orderedVisibleColumns = [];
  
  // 首先添加按顺序排列的列
  columnOrder.forEach(key => {
    if (newVisibleColumns.includes(key)) {
      orderedVisibleColumns.push(key);
    }
  });
  
  // 然后添加不在顺序中但可见的列
  newVisibleColumns.forEach(key => {
    if (!orderedVisibleColumns.includes(key)) {
      orderedVisibleColumns.push(key);
    }
  });
  
  newVisibleColumns = orderedVisibleColumns;
}
```

### 2. 修复列宽一致性问题
**修改前**:
```jsx
// 为可调整的列设置默认宽度，如果没有设置width的话
...(column.resizable !== false && !column.width ? { width: 200 } : {}),
```

**修改后**:
```jsx
// 保持原有的列宽，不强制设置默认宽度
// 移除了强制设置 200px 的逻辑
```

## 🧪 测试验证

### 1. 列顺序恢复测试
1. **打开 InventoryList.tsx 页面**
2. **点击列选择器，拖拽调整列顺序**（如将 Serial Number 拖到最后）
3. **关闭列选择器弹窗**
4. **刷新页面**
5. **检查表格列顺序是否与调整后的一致**

### 2. 列宽一致性测试
1. **记录 resizableColumns=false 时的列宽**
2. **设置 resizableColumns=true**
3. **检查列宽是否与之前一致**
4. **确认没有列被强制设置为 200px**

### 3. 调试日志验证
打开浏览器控制台，应该看到：
```
[WirelessCustomTable] Initializing with saved data: {
  visibleColumns: ["serialNumber", "name", "venue", ...],
  hasColumnOrder: true,
  tableId: "inventory-table"
}
```

### 4. 数据库验证
检查数据库中的 preferences 表，应该看到：
```json
[
  ["table_inventory-table_visible_columns", "[\"serialNumber\",\"name\",\"venue\",...]"],
  ["table_inventory-table_column_order", "[\"name\",\"venue\",\"serialNumber\",...]"]
]
```

## 🔍 预期行为

### ✅ 修复后应该看到

#### 列顺序恢复
1. **拖拽调整**: 在列选择器中拖拽调整列顺序
2. **立即生效**: 表格列顺序立即变化
3. **持久化保存**: 列顺序保存到数据库
4. **正确恢复**: 页面刷新后列顺序与调整后的一致

#### 列宽一致性
1. **保持原宽**: resizableColumns=true 时保持原有列宽
2. **不强制设置**: 不会将未设置宽度的列强制设为 200px
3. **可调整**: 仍然可以拖拽调整列宽
4. **最小宽度**: 保持最小宽度限制（80px）

### ❌ 修复前的问题

#### 列顺序问题
1. **拖拽有效**: 拖拽时列顺序变化
2. **保存成功**: 数据保存到数据库
3. **恢复失败**: 页面刷新后列顺序重置为默认

#### 列宽问题
1. **强制设置**: 未设置宽度的列被强制设为 200px
2. **宽度不一致**: 与 resizableColumns=false 时的宽度不同

## 🚨 常见问题排查

### 问题1: 列顺序仍然不恢复
**检查**:
1. 控制台是否显示 `hasColumnOrder: true`？
2. 数据库中是否有 `column_order` 记录？
3. `visibleColumns` 数组的顺序是否正确？

### 问题2: 列宽仍然不一致
**检查**:
1. 是否移除了强制设置 200px 的逻辑？
2. 原始列定义中是否有 `width` 属性？
3. 是否有其他地方修改了列宽？

### 问题3: 拖拽功能失效
**检查**:
1. ColumnSelector 是否正确接收 `onDragEnd` prop？
2. `handleColumnOrderChange` 是否被正确调用？
3. `columnsOrder` prop 是否为 `true`？

## 📊 修复文件列表

1. **custom_table.jsx** - 修复列顺序恢复逻辑和列宽一致性问题

## 🎯 验证清单

- [ ] 拖拽调整列顺序后表格立即变化
- [ ] 页面刷新后列顺序保持调整后的状态
- [ ] resizableColumns=true 时列宽与原来一致
- [ ] 控制台显示正确的初始化日志
- [ ] 数据库中有对应的 preferences 记录
- [ ] 列宽调整功能仍然正常工作

## 🔄 测试步骤总结

1. **测试列顺序**: 拖拽 → 刷新 → 验证顺序保持
2. **测试列宽**: 对比 resizableColumns 前后的列宽
3. **测试持久化**: 检查数据库记录
4. **测试功能**: 确认所有功能正常工作

现在两个问题都应该得到解决了！
