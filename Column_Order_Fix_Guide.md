# ColumnSelector 列顺序拖拽功能修复指南

## 🔍 问题原因

在移除列宽持久化代码时，意外影响了列顺序功能：

1. **ColumnSelector 组件问题**: 缺少 `onDragEnd` prop，拖拽结束时无法正确保存列顺序
2. **filteredColumns 逻辑问题**: 移除了按 `visibleColumns` 顺序排序的逻辑
3. **回调函数混乱**: 拖拽结束时只调用了 `onChange`，没有调用列顺序保存函数

## 🔧 修复方案

### 1. 修复 ColumnSelector 组件
**文件**: `ampcon-base/frontend/src/modules-smb/Wireless/components/Button/ColumnSelectorProps.tsx`

**主要修改**:
```tsx
// 添加 onDragEnd prop
interface ColumnSelectorProps {
  columns: any[];
  visibleColumns: string[];
  onChange: (visibleColumns: string[]) => void;
  draggable?: boolean;
  onDragEnd?: (newOrder: string[]) => void; // 新增
}

// 修改拖拽结束处理
const handleDragEnd = (result: any) => {
  // ... 拖拽逻辑 ...
  
  // 调用可见列变化回调
  onChange(updatedVisibleColumns);
  
  // 如果提供了拖拽结束回调，也调用它来保存列顺序
  if (onDragEnd) {
    onDragEnd(newOrder);
  }
};
```

### 2. 修复 filteredColumns 排序逻辑
**文件**: `ampcon-base/frontend/src/modules-ampcon/components/custom_table.jsx`

**主要修改**:
```jsx
const filteredColumns = useMemo(() => {
  if (!stableColumns) return [];

  // 筛选可见列
  const visibleKeys = visibleColumns.length > 0 ? visibleColumns : stableColumns.map(col => col.key || col.dataIndex);
  
  // 过滤出可见的列
  const visibleColumnsData = stableColumns.filter(col => {
    const key = col.key || col.dataIndex;
    return visibleKeys.includes(key);
  });
  
  // 按照 visibleColumns 的顺序排序 - 关键修复
  return visibleColumnsData.sort((a, b) => {
    const keyA = a.key || a.dataIndex;
    const keyB = b.key || b.dataIndex;
    const indexA = visibleKeys.indexOf(keyA);
    const indexB = visibleKeys.indexOf(keyB);
    return indexA - indexB;
  });
}, [stableColumns, visibleColumns]);
```

## 🧪 测试验证

### 1. 基本拖拽功能测试
1. **打开 InventoryList.tsx 页面**
2. **点击表格右上角的列选择器按钮**（齿轮图标）
3. **在弹出的列表中拖拽任意行**（上下拖动）
4. **观察表格列的顺序是否发生变化**

### 2. 持久化功能测试
1. **拖拽调整列顺序**
2. **关闭列选择器弹窗**
3. **刷新页面**
4. **检查列顺序是否保持**

### 3. 调试日志验证
打开浏览器控制台，应该看到：
```
ColumnSelector handleDragEnd triggered: { source: {...}, destination: {...} }
[WirelessCustomTable] Saving column order: ["serialNumber", "name", "venue", ...]
```

### 4. 数据库验证
检查数据库中的 preferences 表，应该看到：
```json
[
  ["table_inventory-table_column_order", "[\"serialNumber\",\"name\",\"venue\",...]"]
]
```

## 🔍 预期行为

### ✅ 修复后应该看到
1. **拖拽响应**: 在列选择器中拖拽行时，表格列顺序立即变化
2. **持久化保存**: 列顺序变化会自动保存到数据库
3. **页面刷新**: 刷新页面后列顺序保持不变
4. **调试日志**: 控制台显示拖拽和保存的相关日志

### ❌ 修复前的问题
1. **拖拽无效**: 拖拽行后表格列顺序不变化
2. **无保存**: 没有调用列顺序保存函数
3. **无日志**: 控制台没有相关的调试信息

## 🚨 常见问题排查

### 问题1: 拖拽后列顺序仍然不变化
**检查**:
1. 控制台是否有 `handleDragEnd triggered` 日志？
2. `columnsOrder` prop 是否为 `true`？
3. `draggable` 属性是否正确传递？

### 问题2: 列顺序变化但不持久化
**检查**:
1. 控制台是否有 `Saving column order` 日志？
2. Network 面板是否有 PUT preferences 请求？
3. `tableId` 是否正确设置？

### 问题3: 页面刷新后列顺序重置
**检查**:
1. 数据库中是否有对应的 preferences 记录？
2. 初始化时是否正确加载了保存的列顺序？
3. `shouldEnablePreferences` 是否为 `true`？

## 📊 修复文件列表

1. **ColumnSelectorProps.tsx** - 添加 `onDragEnd` prop 和回调逻辑
2. **custom_table.jsx** - 修复 `filteredColumns` 排序逻辑

## 🎯 验证清单

- [ ] 列选择器中可以拖拽行
- [ ] 拖拽后表格列顺序立即变化
- [ ] 控制台显示拖拽相关日志
- [ ] Network 面板显示保存请求
- [ ] 页面刷新后列顺序保持
- [ ] 数据库中有对应的 preferences 记录

现在列顺序拖拽功能应该完全正常工作了！
